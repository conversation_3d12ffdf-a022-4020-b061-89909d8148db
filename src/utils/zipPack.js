export function zipPack (title, page, swipter_time, pageSize, templateType) {
    if (templateType === 2) {
        return ""
    }
    console.log(title, page, pageSize, "title, page, pageSize");
    const ratio = { "1920": 1.5, "1280": 1, "1440": 1.125 }
    const percentRatio = { "1920": 19.200, "1280": 12.800, "1440": 14.400 }
    const swipterTime = swipter_time;
    const length = Object.keys(page).length;
    console.log(length, "lengthlength", page);

    let SlideItem = ""
    let timeHtml = "";
    let backgroundUrl = ""
    let backgroundSize = ""
    let swiperButtonOpacity = length > 1 ? 0.3 : 0;
    let backgroundRepeat = ""
    pageSize = pageSize.split("x").shift()
    console.log(ratio[pageSize], "pagepagepage", pageSize, ratio);

    for (let item in page) {
        console.log("break");


        backgroundUrl = "";

        let imgHtml = "";
        let pageContent = page[item]
        console.log(pageContent, "pageContentpageContentpageContent");

        pageContent.forEach((item) => {

            // 获取当前页面的背景图显示设置
            backgroundSize = item.background_display || "cover";
            backgroundRepeat = backgroundSize === "repeat" ? "repeat" : "no-repeat";
            if (item.type === 1 && item.template_sm_type === 1) {
                imgHtml += `<img class="img" src="./assets/${item.path}" style="width: ${(item.width / 1280) * 100}%;height: ${(item.height / 720) * 100}%;top: ${(item.y_axis / 720) * 100}%;left: ${(item.x_axis / 1280) * 100}%;" alt="">`;
            } else if (item.type === 2 && item.template_sm_type === 1) {

                imgHtml += `<video class="video vjs-hardware-acceleration" style="width: ${(item.width / 1280) * 100}%; height: ${(item.height / 720) * 100}%; top: ${(item.y_axis / 720) * 100}%;left: ${(item.x_axis / 1280) * 100}%;" autoplay muted playsinline preload id="myVideo">
                    <source  src="./assets/${item.path}" type="video/${item.path.split(".").pop()}" >
                </video>`;

            } else if (item.template_sm_type === 2) {
                // imgHtml += `<div id="datetime" style="width: ${item.width}px;height: ${item.height}px;top: ${item.y_axis}px;left: ${item.x_axis}px;"></div>`;
                // 修改此处的datetime样式（白色背景+黑色字体）
                imgHtml += `<div id="datetime" style="
           width: ${item.width ? item.width : 240}px;
           top: ${(item.y_axis / 720) * 100}%;
           left: ${(item.x_axis / 1280) * 100}%;
           color: #000000; /* 黑色字体 */
           background-color: #ffffff; /* 白色背景 */
           padding: 11px 4px;
           border-radius: 4px;
           font-size: 18px;font-weight: bold;text-align: center;box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);"></div>`;
            }
            if (item.template_sm_type == 2) {
                timeHtml = "updateDateTime();setInterval(updateDateTime, 1000);";
            }
            if (item.template_sm_type == 3) {
                backgroundUrl = item.path;
            }
            if (item.template_sm_type == 5) {
                imgHtml += `<iframe class="img" src="${item.url}" style="width: ${(item.width / 1280) * 100}%;height: ${(item.height / 720) * 100}%;top: ${(item.y_axis / 720) * 100}%;left: ${(item.x_axis / 1280) * 100}%;" frameborder="0" sandbox allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>`;
            }

        });
        SlideItem += `
        <div class="swiper-slide" style="background-image: url(${backgroundUrl ? "./assets/" + backgroundUrl : ""
            }); background-size: ${backgroundSize === "repeat" ? "auto" : backgroundSize
            }; background-repeat: ${backgroundRepeat};background-position: center center";>
            ${imgHtml}
        </div>
        `;
    }

    let html_content = `
  <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fullscreen Swiper</title>
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="./assets/swiper.min.css">
    <style>
     #main {
                width: 100vw;
                height: 100vh;
                position: relative;
            }
           .img {
                position: absolute;
            }
           .video {
                position: absolute;
                background-color: #000;
            }
            #datetime {
                position: absolute;
                color: #ffffff;
            }
           .swiper {
                width: 100%;
                height: 100%;
            }
           .swiper-slide {
                text-align: center;
                font-size: 18px;
                background: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html,
        body {
            height: 100%;
        }

        .swiper-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .swiper-slide {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 5rem;
            color: white;
            text-align: center;
        }

        .slide-1 {
            background-color: #3498db;
        }

        .slide-2 {
            background-color: #e74c3c;
        }

        .slide-3 {
            background-color: #2ecc71;
        }

        .slide-4 {
            background-color: #f39c12;
        }

        .swiper-pagination-bullet {
            width: 12px;
            height: 12px;
            background: #fff;
            opacity: 0.5;
        }

        .swiper-pagination-bullet-active {
            opacity: 1;
        }

        .swiper-button-next,
        .swiper-button-prev {
            color: white;
            opacity: ${swiperButtonOpacity};
        }
        #datetime {
            position: absolute;
            color: #ffffff;
        }
        .vjs-hardware-acceleration {
            -webkit-transform: translateZ(0);
            -moz-transform: translateZ(0);
            -ms-transform: translateZ(0);
            -o-transform: translateZ(0);
            transform: translateZ(0);
            /**或者**/
            transform: rotateZ(360deg);
            transform: translate3d(0, 0, 0);
        }
    </style>
</head>

<body>
    <!-- Swiper -->
    <div class="swiper-container">
        <div class="swiper-wrapper">
        ${SlideItem}

        </div>
        <!-- Add Navigation -->
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
    </div>


</body>
 <!-- jQuery -->
    <script src="./assets/jquery.min.js"></script>
    <!-- Swiper JS -->
    <script src="./assets/swiper.min.js"></script>
    <!-- Initialize Swiper -->
    <script>
    function updateDateTime() {
        const now = new Date();
        const optionsDate = { year: 'numeric', month: 'long', day: 'numeric' };
        const optionsTime = { hour: 'numeric', minute: 'numeric', second: 'numeric' };
        let currentDate = now.toLocaleDateString(undefined, optionsDate);
        let currentTime = now.toLocaleTimeString(undefined, optionsTime);
        const datetimeElements = document.querySelectorAll('#datetime');
        datetimeElements.forEach((element) => {
            element.textContent = currentDate + currentTime;
        });
    }
        ${timeHtml}
        ${length > 1 ? `
            $(document).ready(function () {
            var swiper = new Swiper('.swiper-container', {
                direction: 'horizontal',
                loop: true,
                touchRatio: 1,
                // 可选：设置滑动的灵敏度
                threshold: 10,
               ${swipterTime > 0 ? "autoplay: {delay: " + swipterTime + "* 1000,disableOnInteraction: false}," : ""}
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
            });
        });` : ""}
    </script>
    <script>
    const mainVideo = document.getElementById("myVideo");
    // var videos = ["videoplayback1.mp4", "videoplayback2.mp4"];
    // var index = 0;

    // mainVideo.addEventListener("ended", function () {
    //     index++;
    //     if (index == videos.length) {
    //         index = 0;
    //     }
    //     mainVideo.src = videos[index];

    // });


    mainVideo.addEventListener("ended", function () {
        mainVideo.currentTime = 0;
        mainVideo.play();

    });
    mainVideo.addEventListener("loadedmetadata", function () {
        console.log("loadedmetadata");
        mainVideo.currentTime = 0;
        mainVideo.play();
        // setTimeout(() => {
        //     mainVideo.muted = false; // 必须静音
        // }, 200);

    });

    document.addEventListener('DOMContentLoaded', () => {
        var playPromise = mainVideo.play();

        if (playPromise !== undefined) {
            playPromise.then(_ => {
                // Automatic playback started!
                // Show playing UI.
                // setTimeout(() => {
                //     mainVideo.muted = false; // 必须静音
                // }, 600);

            })
                .catch(error => {
                    // Auto-play was prevented
                    // Show paused UI.
                });
        }
        // if (mainVideo.paused || mainVideo.ended) {
        //     mainVideo.play().catch(error => {
        //         console.log('自动播放失败:', error);
        //         // 可在此添加点击页面任意位置触发播放的代码
        //     });

        // }


    });
</script>
</html>
`;
    return html_content
}