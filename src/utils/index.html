<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fullscreen Swiper</title>
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://unpkg.com/swiper@11.2.8/swiper-bundle.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html,
        body {
            height: 100%;
        }

        .swiper-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .swiper-slide {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 5rem;
            color: white;
            text-align: center;
        }

        .slide-1 {
            background-color: #3498db;
        }

        .slide-2 {
            background-color: #e74c3c;
        }

        .slide-3 {
            background-color: #2ecc71;
        }

        .slide-4 {
            background-color: #f39c12;
        }

        .swiper-pagination-bullet {
            width: 12px;
            height: 12px;
            background: #fff;
            opacity: 0.5;
        }

        .swiper-pagination-bullet-active {
            opacity: 1;
        }

        .swiper-button-next,
        .swiper-button-prev {
            color: white;
        }
    </style>
</head>

<body>
    <!-- Swiper -->
    <div class="swiper-container">
        <div class="swiper-wrapper">
            <div class="swiper-slide slide-1">Slide 1</div>
            <div class="swiper-slide slide-2">Slide 2</div>
        </div>
        <!-- Add Pagination -->
        <div class="swiper-pagination"></div>
        <!-- Add Navigation -->
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-2.2.4.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://unpkg.com/swiper@11.2.8/swiper-bundle.min.js"></script>
    <!-- Initialize Swiper -->
    <script>
        $(document).ready(function () {
            var swiper = new Swiper('.swiper-container', {
                direction: 'horizontal',
                // 确保允许触摸滑动
                touchRatio: 1,
                // 可选：设置滑动的灵敏度
                threshold: 10,
                // 可选：启用模拟触控
                simulateTouch: true,
                loop: true,
                autoplay: {
                    delay: 3000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
            });
        });
    </script>
</body>

</html>