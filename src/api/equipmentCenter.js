import request, { postBlob, getBlob, handleImport } from '@/utils/request'

export function getList (params) {
  return request({
    url: `/admin/equipment/getList`,
    method: 'get',
    params
  })
}

export function getTreeList (params) {
  return request({
    url: `/admin/equipment/getTreeList`,
    method: 'get',
    params
  })
}

export function add (data) {
  return request({
    url: `/manage/equipment/addEquipment`,
    method: 'post',
    data
  })
}

export function edit (data) {
  return request({
    url: `/admin/equipment/editEquipment`,
    method: 'put',
    data
  })
}

export function del (id) {
  return request({
    url: `/admin/equipment/delete/` + id,
    method: 'delete'
  })
}
