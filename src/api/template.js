import request, { postBlob, getBlob, handleImport } from '@/utils/request'

export function getList(params) {
  return request({
    url: `/admin/template/getList`,
    method: 'get',
    params
  })
}

export function getMaterialList(params) {
  return request({
    url: `/admin/sourcematerial/getList`,
    method: 'get',
    params
  })
}

export function getDetail(params) {
  return request({
    url: `/admin/template/getDetail`,
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: `/admin/template/addTemplate`,
    method: 'post',
    data
  })
}

export function edit(data,id) {
  return request({
    url: `/admin/template/edit/` + id,
    method: 'put',
    data
  })
}

export function del(id) {
  return request({
    url: `/admin/template/delete/` + id,
    method: 'delete'
  })
}

export function savePack(data) {
  return request({
    url: `/admin/template/savePack`,
    method: 'post',
    data
  })
}