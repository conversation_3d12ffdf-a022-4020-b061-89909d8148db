import request, { postBlob, getBlob, handleImport } from '@/utils/request'

export function getList(params) {
  return request({
    url: `/admin/sourcematerial/getList`,
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: `/admin/sourcematerial/addSourceMaterial`,
    method: 'post',
    data
  })
}

export function edit(data,id) {
  return request({
    url: `/admin/sourcematerial/editSourceMaterial/` + id,
    method: 'put',
    data
  })
}

export function del(id) {
  return request({
    url: `/admin/sourcematerial/delete/` + id,
    method: 'delete'
  })
}