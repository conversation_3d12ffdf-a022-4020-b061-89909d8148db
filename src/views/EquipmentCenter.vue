<template>
  <div class="equipment-center">
    <!-- 主要内容区域 -->
    <div class="equipment-container">
      <!-- 左侧设备树 -->
      <div class="tree-panel" :style="{ width: treeWidth + 'px' }">
        <el-card class="tree-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">{{ $t('equipmentCenter.tree.title') || '设备列表' }}</span>
          </div>

          <el-input :placeholder="$t('equipmentCenter.tree.searchPlaceholder') || '输入关键字进行过滤'" v-model="filterText"
            clearable prefix-icon="el-icon-search" class="search-input" />

          <el-tree class="equipment-tree" :data="equipmentData" :props="defaultProps" :filter-node-method="filterNode"
            ref="tree" node-key="id" @node-click="handleNodeClick" :render-content="renderContent"
            :highlight-current="true" empty-text="暂无设备数据" @node-expand="handleNodeExpand" :expand-on-click-node="false">
          </el-tree>
        </el-card>
      </div>

      <!-- 拖拽分隔条 -->
      <div class="resize-handle" @mousedown="startResize"></div>

      <!-- 右侧设备详情 -->
      <div class="detail-panel" :style="{ width: 'calc(100% - ' + (treeWidth + 6) + 'px)' }">
        <!-- 未选择设备时的提示 -->
        <div v-if="!selectedDevice" class="empty-state">
          <el-empty :description="$t('equipmentCenter.tree.selectTip') || '请选择左侧设备查看详情'" :image-size="120">
            <template #image>
              <i class="el-icon-s-platform empty-icon"></i>
            </template>
          </el-empty>
        </div>

        <!-- 设备详情内容 -->
        <div v-else class="device-detail">
          <!-- 设备基本信息卡片 -->
          <el-card class="info-card" shadow="never">
            <div slot="header" class="card-header">
              <span class="card-title">{{ $t('equipmentCenter.detail.basicInfo') || '基本信息' }}</span>
              <div class="header-actions">
                <el-button type="primary" size="small" icon="el-icon-edit" @click="editDevice"
                  :disabled="!selectedDevice.isClient">
                  {{ $t('equipmentCenter.button.edit') || '编辑' }}
                </el-button>
                <el-button type="danger" size="small" icon="el-icon-delete" @click="deleteDevice"
                  :disabled="!selectedDevice.isClient">
                  {{ $t('equipmentCenter.button.delete') || '删除' }}
                </el-button>
              </div>
            </div>

            <div class="device-info">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.table.name') || '设备名称' }}:</span>
                    <span class="info-value">{{ selectedDevice.label || '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.table.alias_name') || '设备别名' }}:</span>
                    <span class="info-value">{{ selectedDevice.alias_name || '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.form.mac') || 'MAC地址' }}:</span>
                    <span class="info-value">{{ selectedDevice.mac_address || '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.table.group_name') || '分组名称' }}:</span>
                    <span class="info-value">{{ selectedDevice.group_name || '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.table.status') || '设备状态' }}:</span>
                    <el-tag :type="selectedDevice.isOnline ? 'success' : 'danger'" size="small">
                      {{ selectedDevice.isOnline ? ($t('equipmentCenter.table.online') || '在线') :
                        ($t('equipmentCenter.table.offline') || '离线') }}
                    </el-tag>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.table.updated_at') || '最后上线时间' }}:</span>
                    <span class="info-value">{{ formatTime(selectedDevice.updated_at) || '-' }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>

          <!-- 设备统计信息卡片（仅对分组显示） -->
          <el-card v-if="!selectedDevice.isClient" class="stats-card" shadow="never">
            <div slot="header" class="card-header">
              <span class="card-title">{{ $t('equipmentCenter.detail.statsInfo') || '统计信息' }}</span>
            </div>

            <div class="stats-info">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ selectedDevice.total || 0 }}</div>
                    <div class="stat-label">{{ $t('equipmentCenter.stats.total') || '设备总数' }}</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item online">
                    <div class="stat-number">{{ selectedDevice.online || 0 }}</div>
                    <div class="stat-label">{{ $t('equipmentCenter.stats.online') || '在线设备' }}</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item offline">
                    <div class="stat-number">{{ (selectedDevice.total || 0) - (selectedDevice.online || 0) }}</div>
                    <div class="stat-label">{{ $t('equipmentCenter.stats.offline') || '离线设备' }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>
      </div>
    </div>


    <el-dialog :title="$t('equipmentCenter.dialog.title.edit')" :close-on-click-modal="false" :visible.sync="isShow"
      width="30%" @close="close">
      <el-form :model="deviceInfo" :rules="rules" ref="deviceInfo" label-width="80px" label-position="left"
        class="demo-ruleForm">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('equipmentCenter.table.group_name')" prop="group_name" required>
              <el-input v-model="deviceInfo.group_name" :disabled="true"
                :placeholder="$t('equipmentCenter.form.namePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('equipmentCenter.table.alias_name')" prop="alias_name" required>
              <el-input v-model="deviceInfo.alias_name"
                :placeholder="$t('equipmentCenter.form.namePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('equipmentCenter.form.mac')" prop="mac_address" required>
              <el-input v-model="deviceInfo.mac_address" :disabled="true"
                :placeholder="$t('equipmentCenter.form.macPlaceholder')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="save()">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getTreeList, edit, del } from '@/api/equipmentCenter.js'

export default {
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    }
  },
  data () {
    return {
      equipmentData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isClient: 'isClient',
        mac_address: 'mac_address',
      },
      filterText: "",
      selectedDevice: null, // 当前选中的设备
      treeWidth: 250, // 左侧树的宽度，默认250px
      isResizing: false, // 是否正在调整大小
      pageNum: 1,
      pageSize: 10,
      total: 0,
      form: {
        alias_name: '',
        mac_address: '',
      },
      deviceInfo: {
        mac_address: '',
        alias_name: '',
        group_name: '',
      },
      id: '',
      rules: {
        alias_name: [
          { required: true, message: this.$t('equipmentCenter.form.namePlaceholder'), trigger: 'blur' },
        ],
        mac_address: [
          { required: true, message: this.$t('equipmentCenter.form.macPlaceholder'), trigger: 'blur' },
        ],
        group_name: [
          { required: true, message: this.$t('equipmentCenter.form.namePlaceholder'), trigger: 'blur' },
        ],
      },
      isShow: false,
      isEdit: false,
      isLoading: false
    }
  },
  created () {
    this.getList()
  },
  methods: {
    handleNodeExpand () {
      // 节点展开事件处理
    },
    renderContent (h, { node, data }) {
      const isClient = data.isClient;
      const isSelected = this.selectedDevice && this.selectedDevice.id === data.id;

      return (
        <span class={['custom-tree-node', { 'selected-node': isSelected }]}>
          <span class="node-content">
            <i class={[
              isClient ? 'el-icon-monitor' : (!node.expanded ? 'el-icon-folder' : 'el-icon-folder-opened'),
              'node-icon',
              { 'online-icon': isClient && data.isOnline, 'offline-icon': isClient && !data.isOnline }
            ]}></i>
            <span class="node-label">{node.label}</span>
            {isClient && (
              <el-tag
                size="mini"
                type={data.isOnline ? 'success' : 'danger'}
                class="status-tag"
              >
                {data.isOnline ? '在线' : '离线'}
              </el-tag>
            )}
          </span>
          <span class="node-extra">
            {!isClient && (
              <span class="device-count">
                <span class="online-count">{data.online || 0}</span>
                /
                <span class="total-count">{data.total || 0}</span>
              </span>
            )}
          </span>
        </span>
      );
    },

    handleNodeClick (data) {
      console.log(data, "节点点击");

      // 设置当前选中的设备
      this.selectedDevice = data;

      // 高亮当前节点
      this.$refs.tree.setCurrentKey(data.id);
    },
    filterNode (value, data) {
      console.log(value, data, "value, data", data.mac_address);
      // let result
      if (!value) return true;
      return data.label.indexOf(value) !== -1 || data.mac_address?.toLowerCase().indexOf(value) !== -1;


    },
    getList () {
      this.isLoading = true
      getTreeList().then(res => {
        if (res.code == 0) {
          this.equipmentData = res.data
          this.isLoading = false
        }
      })
    },

    // 格式化时间
    formatTime (timestamp) {
      if (!timestamp) return '-';
      return this.$formatTimeStamp ? this.$formatTimeStamp(timestamp) : new Date(timestamp).toLocaleString();
    },

    // 编辑设备
    editDevice () {
      if (!this.selectedDevice || !this.selectedDevice.isClient) {
        this.$message.warning('请选择要编辑的设备');
        return;
      }

      this.deviceInfo.alias_name = this.selectedDevice.alias_name || '';
      this.deviceInfo.mac_address = this.selectedDevice.mac_address || '';
      this.deviceInfo.group_name = this.selectedDevice.group_name || '';
      this.isEdit = true;
      this.isShow = true;
    },

    // 删除设备
    deleteDevice () {
      if (!this.selectedDevice || !this.selectedDevice.isClient) {
        this.$message.warning('请选择要删除的设备');
        return;
      }

      this.$confirm(
        this.$t('equipmentCenter.table.sureToDelete') || '确定删除该设备?',
        this.$t('public.confirm') || '提示',
        {
          confirmButtonText: this.$t('public.confirm') || '确定',
          cancelButtonText: this.$t('public.cancel') || '取消',
          type: 'warning'
        }
      ).then(() => {
        this.del(this.selectedDevice.id);
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 新建模板
    add () {
      this.isShow = true
    },
    edit (row) {
      this.deviceInfo.alias_name = row.alias_name
      this.deviceInfo.mac_address = row.mac_address
      this.deviceInfo.group_name = row.group_name

      this.isShow = true
    },
    save () {
      this.$refs.deviceInfo.validate((valid) => {
        if (valid) {
          edit({
            group_name: this.deviceInfo.group_name,
            alias_name: this.deviceInfo.alias_name,
            mac_address: this.deviceInfo.mac_address,
          }).then(() => {
            this.$message({
              type: 'success',
              message: this.$t('public.editSuccess')
            });

            // 更新选中设备的信息
            if (this.selectedDevice) {
              this.selectedDevice.alias_name = this.deviceInfo.alias_name;
              this.selectedDevice.group_name = this.deviceInfo.group_name;
            }

            this.isShow = false;
            this.getList(); // 刷新设备列表
          }).catch(() => {
            this.$message({
              type: 'error',
              message: '保存失败，请重试'
            });
          });
        }
      });
    },
    del (id) {
      del(id).then(() => {
        this.$message({
          type: 'success',
          message: this.$t('public.deleteSuccess')
        });

        // 清空选中设备
        this.selectedDevice = null;
        this.getList(); // 刷新设备列表
      }).catch(() => {
        this.$message({
          type: 'error',
          message: '删除失败，请重试'
        });
      });
    },
    close () {
      this.addForm = {
        name: '',
        password: '',
        account: ''
      }
    },
    // 搜索
    searchForm () {
      this.getList()
    },
    // 重置
    resetForm () {
      this.page = 1
      this.$refs.form.resetFields();
      this.getList()
    },
    handleSizeChange (val) {
      this.pageNum = 1
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },

    // 开始拖拽调整宽度
    startResize (e) {
      this.isResizing = true;
      const startX = e.clientX;
      const startWidth = this.treeWidth;

      const handleMouseMove = (e) => {
        if (!this.isResizing) return;

        const deltaX = e.clientX - startX;
        const newWidth = startWidth + deltaX;

        // 限制最小和最大宽度
        if (newWidth >= 150 && newWidth <= 500) {
          this.treeWidth = newWidth;
        }
      };

      const handleMouseUp = () => {
        this.isResizing = false;
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }
  },
};
</script>
<style>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.custom-tree-node:hover {
  background-color: #f5f7fa;
}

.custom-tree-node.selected-node {
  background-color: #ecf5ff;
  border: 1px solid #409eff;
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.node-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #606266;
}

.node-icon.online-icon {
  color: #67c23a;
}

.node-icon.offline-icon {
  color: #f56c6c;
}

.node-label {
  margin-right: 8px;
  color: #303133;
}

.status-tag {
  margin-left: 8px;
}

.node-extra {
  display: flex;
  align-items: center;
}

.device-count {
  font-size: 12px;
  color: #909399;
  background-color: #f4f4f5;
  padding: 2px 6px;
  border-radius: 10px;
}

.online-count {
  color: #67c23a;
  font-weight: 500;
}

.total-count {
  color: #606266;
}
</style>

<style scoped>
.equipment-center {
  padding: 0;
  background-color: #fff;
}

.equipment-container {
  height: 80vh;
  background-color: #ccc;
  margin: 10px 0;
  display: flex;
  position: relative;
}

.tree-panel {
  height: 100%;
  flex-shrink: 0;
  position: relative;
}

.resize-handle {
  width: 6px;
  height: 100%;
  background-color: #e4e7ed;
  cursor: col-resize;
  position: relative;
  flex-shrink: 0;
  transition: background-color 0.2s ease;
}

.resize-handle:hover {
  background-color: #409eff;
}

.resize-handle::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 20px;
  background-color: #909399;
  border-radius: 1px;
}

.tree-card {
  height: 100%;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.tree-card .el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.tree-card .el-card__body {
  padding: 0;
  height: calc(100% - 10px);
  overflow: hidden;
  background-color: #fff;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.search-input {
  margin: 16px;
  margin-bottom: 8px;
}

.equipment-tree {
  padding: 8px 16px;
  height: calc(100% - 60px);
  overflow-y: auto;
}

.detail-panel {
  height: 100%;
  flex: 1;
  overflow: hidden;
  padding-left: 10px;
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 1px solid #ebeef5;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
}

.device-detail {
  height: 100%;
  overflow-y: auto;
}

.info-card,
.stats-card {
  margin-bottom: 16px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.info-card:last-child,
.stats-card:last-child {
  margin-bottom: 0;
}

.info-card .el-card__header,
.stats-card .el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.device-info {
  padding: 16px 20px;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  line-height: 1.5;
}

.info-label {
  font-weight: 500;
  color: #606266;
  margin-right: 12px;
  min-width: 90px;
  font-size: 13px;
}

.info-value {
  color: #303133;
  flex: 1;
  font-size: 13px;
}

.stats-info {
  padding: 16px 20px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.stat-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-item.online {
  background-color: #f0f9ff;
  border-color: #b3d8ff;
}

.stat-item.offline {
  background-color: #fef0f0;
  border-color: #fbc4c4;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-item.online .stat-number {
  color: #67c23a;
}

.stat-item.offline .stat-number {
  color: #f56c6c;
}

.stat-label {
  font-size: 12px;
  color: #606266;
  line-height: 1;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .equipment-container {
    flex-direction: column;
    height: auto;
  }

  .tree-panel,
  .detail-panel {
    width: 100% !important;
    flex: none;
    padding: 0;
  }

  .tree-panel {
    margin-bottom: 16px;
  }

  .tree-card {
    height: 400px;
  }

  .resize-handle {
    display: none;
  }
}

@media (max-width: 768px) {
  .equipment-center {
    padding: 0;
  }

  .equipment-container {
    margin: 5px 0;
  }

  .header-actions {
    flex-direction: column;
    gap: 4px;
  }

  .header-actions .el-button {
    width: 100%;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .info-label {
    margin-bottom: 4px;
    min-width: auto;
  }

  .stat-item {
    padding: 12px;
  }

  .stat-number {
    font-size: 20px;
  }
}
</style>