<template>
  <div>

    <el-row class="equipment-tree">
      <el-col :span="7" style="height:100%;border-right: 1px solid #eee;overflow-y: auto;">

        <el-input placeholder="输入关键字进行过滤" v-model="filterText" clearable />

        <el-tree class="filter-tree" :data="equipmentData" :props="defaultProps" :filter-node-method="filterNode"
          ref="tree" node-key="id" @node-click="handleNodeClick" :render-content="renderContent" show-checkbox
          :highlight-current="true" empty-text="null" @node-expand="handleNodeExpand">
        </el-tree>
      </el-col>
      <el-col :span="14">
        dsfsdfds
      </el-col>

    </el-row>


    <el-dialog :title="$t('equipmentCenter.dialog.title.edit')" :close-on-click-modal="false" :visible.sync="isShow"
      width="30%" @close="close">
      <el-form :model="deviceInfo" :rules="rules" ref="deviceInfo" label-width="80px" label-position="left"
        class="demo-ruleForm">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('equipmentCenter.table.group_name')" prop="group_name" required>
              <el-input v-model="deviceInfo.group_name" :disabled="true"
                :placeholder="$t('equipmentCenter.form.namePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('equipmentCenter.table.alias_name')" prop="alias_name" required>
              <el-input v-model="deviceInfo.alias_name"
                :placeholder="$t('equipmentCenter.form.namePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('equipmentCenter.form.mac')" prop="mac_address" required>
              <el-input v-model="deviceInfo.mac_address" :disabled="true"
                :placeholder="$t('equipmentCenter.form.macPlaceholder')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="save()">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getList, getTreeList, add, edit, del } from '@/api/equipmentCenter.js'

export default {
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    }
  },
  data () {
    return {
      equipmentData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isClient: 'isClient',
        mac_address: 'mac_address',

      },
      filterText: "",
      pageNum: 1,
      pageSize: 10,
      total: 0,
      form: {
        alias_name: '',
        mac_address: '',
      },
      deviceInfo: {
        mac_address: '',
        alias_name: '',
        group_name: '',
      },
      id: '',
      rules: {
        name: [
          { required: true, message: this.$t('equipmentCenter.dialog.form.accountNamePlaceholder'), trigger: 'blur' },
        ],
        password: [
          { required: true, message: this.$t('equipmentCenter.dialog.form.passwordPlaceholder'), trigger: 'blur' },
        ],
        account: [
          { required: true, message: this.$t('equipmentCenter.dialog.form.accountPlaceholder'), trigger: 'blur' },
        ],
      },
      isShow: false,
      isEdit: false,
      isLoading: false
    }
  },
  created () {
    this.getList()
  },
  methods: {
    handleNodeExpand (data, node, ref) {
      // ref.$refs.node.
      console.log("节点展开", arguments);
      // this.$refs.tree.filter('');
    },
    renderContent (h, { node, data }) {
      const isClient = data.isClient;
      const isDisabled = !isClient || (this.selectedNode && this.selectedNode.id !== data.id);


      return (
        <span class="custom-tree-node">
          <span class={[isClient ? 'el-icon-s-platform' : '',
          data.isOnline ? 'online-icon' : '',
          !node.expanded ? 'el-icon-folder' : 'el-icon-folder-opened'
          ]}>{node.label}</span>
          <span>
            <span>{isClient ? '' : '(' + data.online + '/' + data.total + ')'}</span>
            {/* <el-button size="mini" type="text" on-click={() => this.remove(node, data)}>{this.$t('equipmentCenter.button.edit')}</el-button>
            <el-button size="mini" type="text" on-click={() => this.singleSend(data)}>{this.$t('equipmentCenter.button.singleSend')}</el-button>
            <el-button size="mini" type="text" on-click={() => this.remove(node, data)}>{this.$t('equipmentCenter.button.delete')}</el-button> */}
          </span>
        </span >);
    },
    // 禁用其他节点
    disableOtherNodes (currentId) {
      const getAllLeafIds = (nodes) => {
        let ids = [];
        nodes.forEach(node => {
          if (node.isClient && node.id !== currentId) {
            ids.push(node.id);
          }
          if (node.children) {
            ids = ids.concat(getAllLeafIds(node.children));
          }
        });
        return ids;
      };

      this.disabledNodeIds = getAllLeafIds(this.equipmentData).filter(id => id !== currentId);
    },
    handleCheckChange (node, checked) {
      if (checked) {
        // 使用getCheckedNodes获取所有选中节点
        const checkedNodes = this.$refs.tree.getCheckedNodes();
        const leafNodes = checkedNodes.filter(n => n.isClient);

        if (leafNodes.length > 0) {
          // 只保留最后一个选中的叶子节点
          const lastSelected = leafNodes[leafNodes.length - 1];
          this.$refs.tree.setCheckedNodes([lastSelected], false);
          this.selectedNode = lastSelected;

          // 禁用其他节点
          this.disableOtherNodes(lastSelected.id);
        }
      } else {
        // 节点取消选中时清空选择
        this.selectedNode = null;
      }
    },
    handleNodeClick (data, node, ref) {
      if (!data.isClient) {
        return;
      }
      console.log(data, "节点点击", node);

      // 如果不是叶子节点则返回
      if (!node.isClient) {
        return;
      }

      // 如果点击的是已选中的节点，则取消选择
      if (this.selectedNode && this.selectedNode.id === node.id) {
        this.selectedNode = null;
        this.disabledNodes = [];
        this.$refs.tree.setCheckedKeys([]);
      } else {
        // 选择新节点
        this.selectedNode = node;
        this.$refs.tree.setCheckedKeys([node.id]);
        this.disableOtherNodes(node.id);
      }
    },
    filterNode (value, data) {
      console.log(value, data, "value, data", data.mac_address);
      // let result
      if (!value) return true;
      return data.label.indexOf(value) !== -1 || data.mac_address?.toLowerCase().indexOf(value) !== -1;


    },
    getList () {
      this.isLoading = true
      getTreeList().then(res => {
        if (res.code == 0) {
          this.equipmentData = res.data
          this.isLoading = false
        }
      })
    },

    // 新建模板
    add () {
      this.isShow = true
    },
    edit (row) {
      this.deviceInfo.alias_name = row.alias_name
      this.deviceInfo.mac_address = row.mac_address
      this.deviceInfo.group_name = row.group_name

      this.isShow = true
    },
    save () {
      this.$refs.deviceInfo.validate((valid) => {
        if (valid) {

          edit({
            group_name: this.deviceInfo.group_name,
            alias_name: this.deviceInfo.alias_name,
            mac_address: this.deviceInfo.mac_address,

          }).then(res => {
            this.$message({
              type: 'success',
              message: this.$t('public.editSuccess')
            });

            this.isShow = false
          })

        }
      });
    },
    del (id) {
      del(id).then(res => {
        this.$message({
          type: 'success',
          message: this.$t('public.deleteSuccess')
        });
        this.getList()
      })
    },
    close () {
      this.addForm = {
        name: '',
        password: '',
        account: ''
      }
    },
    // 搜索
    searchForm () {
      this.getList()
    },
    // 重置
    resetForm () {
      this.page = 1
      this.$refs.form.resetFields();
      this.getList()
    },
    handleSizeChange (val) {
      this.pageNum = 1
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    }
  },
};
</script>
<style>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.online-icon::before {
  color: #67c23a;
}
</style>
<style scoped>
.equipment-tree {
  min-width: 300px;
  max-width: 600px;
  height: 80vh;

}



.flex {
  display: flex;
}

.img {
  width: 40px;
  height: 40px;
}
</style>