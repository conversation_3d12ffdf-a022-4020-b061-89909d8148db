<template>
  <div class="equipment-center">
    <!-- 主要内容区域 -->
    <div class="equipment-container">
      <!-- 左侧设备树 -->
      <div class="tree-panel" :style="{ width: treeWidth + 'px' }">
        <el-card class="tree-card" shadow="never">
          <div slot="header" class="card-header">
            <span class="card-title">{{ $t('equipmentCenter.tree.title') || '设备列表' }}</span>
          </div>

          <el-input :placeholder="$t('equipmentCenter.tree.searchPlaceholder') || '输入关键字进行过滤'" v-model="filterText"
            clearable prefix-icon="el-icon-search" class="search-input" />

          <el-tree class="equipment-tree" :data="equipmentData" :props="defaultProps" :filter-node-method="filterNode"
            ref="tree" node-key="id" @node-click="handleNodeClick" :render-content="renderContent"
            :highlight-current="true" empty-text="暂无设备数据" @node-expand="handleNodeExpand" :expand-on-click-node="false">
          </el-tree>
        </el-card>
      </div>

      <!-- 拖拽分隔条 -->
      <div class="resize-handle" @mousedown="startResize"></div>

      <!-- 右侧设备详情 -->
      <div class="detail-panel" :style="{ width: 'calc(100% - ' + (treeWidth + 8) + 'px)' }">
        <!-- 未选择设备时的提示 -->
        <div v-if="!selectedDevice" class="empty-state">
          <el-empty :description="$t('equipmentCenter.tree.selectTip') || '请选择左侧设备查看详情'" :image-size="120">
            <template #image>
              <i class="el-icon-s-platform empty-icon"></i>
            </template>
          </el-empty>
        </div>

        <!-- 设备详情内容 -->
        <div v-else class="device-detail">
          <!-- 设备基本信息卡片 -->
          <el-card class="info-card" shadow="never">
            <div slot="header" class="card-header">
              <span class="card-title">{{ $t('equipmentCenter.detail.basicInfo') || '基本信息' }}</span>
              <div class="header-actions">
                <el-button type="primary" size="small" icon="el-icon-edit" @click="editDevice"
                  :disabled="!selectedDevice.isClient">
                  {{ $t('equipmentCenter.button.edit') || '编辑' }}
                </el-button>
                <el-button type="danger" size="small" icon="el-icon-delete" @click="deleteDevice"
                  :disabled="!selectedDevice.isClient">
                  {{ $t('equipmentCenter.button.delete') || '删除' }}
                </el-button>
              </div>
            </div>

            <div class="device-info">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.table.name') || '设备名称' }}:</span>
                    <span class="info-value">{{ selectedDevice.label || '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.table.alias_name') || '设备别名' }}:</span>
                    <span class="info-value">{{ selectedDevice.alias_name || '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.form.mac') || 'MAC地址' }}:</span>
                    <span class="info-value">{{ selectedDevice.mac_address || '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.table.group_name') || '分组名称' }}:</span>
                    <span class="info-value">{{ selectedDevice.group_name || '-' }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.table.status') || '设备状态' }}:</span>
                    <el-tag :type="selectedDevice.isOnline ? 'success' : 'danger'" size="small">
                      {{ selectedDevice.isOnline ? ($t('equipmentCenter.table.online') || '在线') :
                        ($t('equipmentCenter.table.offline') || '离线') }}
                    </el-tag>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">{{ $t('equipmentCenter.table.updated_at') || '最后上线时间' }}:</span>
                    <span class="info-value">{{ formatTime(selectedDevice.updated_at) || '-' }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>

          <!-- 设备统计信息卡片（仅对分组显示） -->
          <el-card v-if="!selectedDevice.isClient" class="stats-card" shadow="never">
            <div slot="header" class="card-header">
              <span class="card-title">{{ $t('equipmentCenter.detail.statsInfo') || '统计信息' }}</span>
            </div>

            <div class="stats-info">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="stat-item">
                    <div class="stat-number">{{ selectedDevice.total || 0 }}</div>
                    <div class="stat-label">{{ $t('equipmentCenter.stats.total') || '设备总数' }}</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item online">
                    <div class="stat-number">{{ selectedDevice.online || 0 }}</div>
                    <div class="stat-label">{{ $t('equipmentCenter.stats.online') || '在线设备' }}</div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="stat-item offline">
                    <div class="stat-number">{{ (selectedDevice.total || 0) - (selectedDevice.online || 0) }}</div>
                    <div class="stat-label">{{ $t('equipmentCenter.stats.offline') || '离线设备' }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>
      </div>
    </div>


    <el-dialog :title="$t('equipmentCenter.dialog.title.edit')" :close-on-click-modal="false" :visible.sync="isShow"
      width="30%" @close="close">
      <el-form :model="deviceInfo" :rules="rules" ref="deviceInfo" label-width="80px" label-position="left"
        class="demo-ruleForm">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('equipmentCenter.table.group_name')" prop="group_name" required>
              <el-input v-model="deviceInfo.group_name" :disabled="true"
                :placeholder="$t('equipmentCenter.form.namePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('equipmentCenter.table.alias_name')" prop="alias_name" required>
              <el-input v-model="deviceInfo.alias_name"
                :placeholder="$t('equipmentCenter.form.namePlaceholder')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('equipmentCenter.form.mac')" prop="mac_address" required>
              <el-input v-model="deviceInfo.mac_address" :disabled="true"
                :placeholder="$t('equipmentCenter.form.macPlaceholder')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">{{ $t('public.cancel') }}</el-button>
        <el-button type="primary" @click="save()">{{ $t('public.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getTreeList, edit, del } from '@/api/equipmentCenter.js'

export default {
  watch: {
    filterText (val) {
      this.$refs.tree.filter(val);
    }
  },
  data () {
    return {
      equipmentData: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isClient: 'isClient',
        mac_address: 'mac_address',
      },
      filterText: "",
      selectedDevice: null, // 当前选中的设备
      treeWidth: 250, // 左侧树的宽度，默认250px
      isResizing: false, // 是否正在调整大小
      pageNum: 1,
      pageSize: 10,
      total: 0,
      form: {
        alias_name: '',
        mac_address: '',
      },
      deviceInfo: {
        mac_address: '',
        alias_name: '',
        group_name: '',
      },
      id: '',
      rules: {
        alias_name: [
          { required: true, message: this.$t('equipmentCenter.form.namePlaceholder'), trigger: 'blur' },
        ],
        mac_address: [
          { required: true, message: this.$t('equipmentCenter.form.macPlaceholder'), trigger: 'blur' },
        ],
        group_name: [
          { required: true, message: this.$t('equipmentCenter.form.namePlaceholder'), trigger: 'blur' },
        ],
      },
      isShow: false,
      isEdit: false,
      isLoading: false
    }
  },
  created () {
    this.getList()
  },
  methods: {
    handleNodeExpand () {
      // 节点展开事件处理
    },
    renderContent (h, { node, data }) {
      const isClient = data.isClient;
      const isSelected = this.selectedDevice && this.selectedDevice.id === data.id;

      return (
        <span class={['custom-tree-node', { 'selected-node': isSelected }]}>
          <span class="node-content">
            <i class={[
              isClient ? 'el-icon-monitor' : (!node.expanded ? 'el-icon-folder' : 'el-icon-folder-opened'),
              'node-icon',
              { 'online-icon': isClient && data.isOnline, 'offline-icon': isClient && !data.isOnline }
            ]}></i>
            <span class="node-label">{node.label}</span>
            {isClient && (
              <el-tag
                size="mini"
                type={data.isOnline ? 'success' : 'danger'}
                class="status-tag"
              >
                {data.isOnline ? '在线' : '离线'}
              </el-tag>
            )}
          </span>
          <span class="node-extra">
            {!isClient && (
              <span class="device-count">
                <span class="online-count">{data.online || 0}</span>
                /
                <span class="total-count">{data.total || 0}</span>
              </span>
            )}
          </span>
        </span>
      );
    },

    handleNodeClick (data) {
      console.log(data, "节点点击");

      // 设置当前选中的设备
      this.selectedDevice = data;

      // 高亮当前节点
      this.$refs.tree.setCurrentKey(data.id);
    },
    filterNode (value, data) {
      console.log(value, data, "value, data", data.mac_address);
      // let result
      if (!value) return true;
      return data.label.indexOf(value) !== -1 || data.mac_address?.toLowerCase().indexOf(value) !== -1;


    },
    getList () {
      this.isLoading = true
      getTreeList().then(res => {
        if (res.code == 0) {
          this.equipmentData = res.data
          this.isLoading = false
        }
      })
    },

    // 格式化时间
    formatTime (timestamp) {
      if (!timestamp) return '-';
      return this.$formatTimeStamp ? this.$formatTimeStamp(timestamp) : new Date(timestamp).toLocaleString();
    },

    // 编辑设备
    editDevice () {
      if (!this.selectedDevice || !this.selectedDevice.isClient) {
        this.$message.warning('请选择要编辑的设备');
        return;
      }

      this.deviceInfo.alias_name = this.selectedDevice.alias_name || '';
      this.deviceInfo.mac_address = this.selectedDevice.mac_address || '';
      this.deviceInfo.group_name = this.selectedDevice.group_name || '';
      this.isEdit = true;
      this.isShow = true;
    },

    // 删除设备
    deleteDevice () {
      if (!this.selectedDevice || !this.selectedDevice.isClient) {
        this.$message.warning('请选择要删除的设备');
        return;
      }

      this.$confirm(
        this.$t('equipmentCenter.table.sureToDelete') || '确定删除该设备?',
        this.$t('public.confirm') || '提示',
        {
          confirmButtonText: this.$t('public.confirm') || '确定',
          cancelButtonText: this.$t('public.cancel') || '取消',
          type: 'warning'
        }
      ).then(() => {
        this.del(this.selectedDevice.id);
      }).catch(() => {
        // 用户取消删除
      });
    },

    // 新建模板
    add () {
      this.isShow = true
    },
    edit (row) {
      this.deviceInfo.alias_name = row.alias_name
      this.deviceInfo.mac_address = row.mac_address
      this.deviceInfo.group_name = row.group_name

      this.isShow = true
    },
    save () {
      this.$refs.deviceInfo.validate((valid) => {
        if (valid) {
          edit({
            group_name: this.deviceInfo.group_name,
            alias_name: this.deviceInfo.alias_name,
            mac_address: this.deviceInfo.mac_address,
          }).then(() => {
            this.$message({
              type: 'success',
              message: this.$t('public.editSuccess')
            });

            // 更新选中设备的信息
            if (this.selectedDevice) {
              this.selectedDevice.alias_name = this.deviceInfo.alias_name;
              this.selectedDevice.group_name = this.deviceInfo.group_name;
            }

            this.isShow = false;
            this.getList(); // 刷新设备列表
          }).catch(() => {
            this.$message({
              type: 'error',
              message: '保存失败，请重试'
            });
          });
        }
      });
    },
    del (id) {
      del(id).then(() => {
        this.$message({
          type: 'success',
          message: this.$t('public.deleteSuccess')
        });

        // 清空选中设备
        this.selectedDevice = null;
        this.getList(); // 刷新设备列表
      }).catch(() => {
        this.$message({
          type: 'error',
          message: '删除失败，请重试'
        });
      });
    },
    close () {
      this.addForm = {
        name: '',
        password: '',
        account: ''
      }
    },
    // 搜索
    searchForm () {
      this.getList()
    },
    // 重置
    resetForm () {
      this.page = 1
      this.$refs.form.resetFields();
      this.getList()
    },
    handleSizeChange (val) {
      this.pageNum = 1
      this.pageSize = val
      this.getList()
    },
    handleCurrentChange (val) {
      this.pageNum = val
      this.getList()
    },

    // 开始拖拽调整宽度
    startResize (e) {
      this.isResizing = true;
      const startX = e.clientX;
      const startWidth = this.treeWidth;

      const handleMouseMove = (e) => {
        if (!this.isResizing) return;

        const deltaX = e.clientX - startX;
        const newWidth = startWidth + deltaX;

        // 限制最小和最大宽度
        if (newWidth >= 150 && newWidth <= 500) {
          this.treeWidth = newWidth;
        }
      };

      const handleMouseUp = () => {
        this.isResizing = false;
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }
  },
};
</script>
<style>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
  margin: 1px 0;
}

.custom-tree-node:hover {
  background-color: #f0f2f5;
  transform: translateX(2px);
}

.custom-tree-node.selected-node {
  background: linear-gradient(135deg, #ecf5ff 0%, #e1f0ff 100%);
  border: 1px solid #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.node-icon {
  margin-right: 10px;
  font-size: 16px;
  color: #606266;
  transition: color 0.2s ease;
}

.node-icon.online-icon {
  color: #67c23a;
  text-shadow: 0 0 4px rgba(103, 194, 58, 0.3);
}

.node-icon.offline-icon {
  color: #f56c6c;
  text-shadow: 0 0 4px rgba(245, 108, 108, 0.3);
}

.node-label {
  margin-right: 8px;
  color: #303133;
  font-weight: 500;
  font-size: 13px;
}

.status-tag {
  margin-left: 8px;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
}

.node-extra {
  display: flex;
  align-items: center;
}

.device-count {
  font-size: 11px;
  color: #909399;
  background: linear-gradient(135deg, #f4f4f5 0%, #e9e9e9 100%);
  padding: 3px 8px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  font-weight: 500;
}

.online-count {
  color: #67c23a;
  font-weight: 600;
}

.total-count {
  color: #606266;
  font-weight: 500;
}
</style>

<style scoped>
.equipment-center {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.equipment-container {
  height: calc(100vh - 120px);
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  position: relative;
  overflow: hidden;
}

.tree-panel {
  height: 100%;
  flex-shrink: 0;
  position: relative;
}

.resize-handle {
  width: 8px;
  height: 100%;
  background-color: #f5f7fa;
  cursor: col-resize;
  position: relative;
  flex-shrink: 0;
  transition: all 0.2s ease;
  border-left: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
}

.resize-handle:hover {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.resize-handle::before {
  content: '';
  width: 3px;
  height: 40px;
  background: linear-gradient(to bottom,
      transparent 0%,
      #c0c4cc 20%,
      #c0c4cc 80%,
      transparent 100%);
  border-radius: 2px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.resize-handle:hover::before {
  opacity: 1;
  background: linear-gradient(to bottom,
      transparent 0%,
      #409eff 20%,
      #409eff 80%,
      transparent 100%);
}

.tree-card {
  height: 100%;
  border: none;
  border-radius: 0;
  box-shadow: none;
  background-color: #fafbfc;
}

.tree-card .el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f8f9fa;
  border-radius: 0;
}

.tree-card .el-card__body {
  padding: 0;
  height: calc(100% - 60px);
  overflow: hidden;
  background-color: #fafbfc;
}

.tree-card .el-card__body .el-main {
  padding: 1px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.search-input {
  margin: 12px 16px;
  margin-bottom: 8px;
}

.search-input .el-input__inner {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  background-color: #fff;
}

.search-input .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.equipment-tree {
  padding: 8px 12px;
  height: calc(100% - 60px);
  overflow-y: auto;
  background-color: #fafbfc;
}

.equipment-tree .el-tree-node {
  margin-bottom: 2px;
}

.equipment-tree .el-tree-node__content {
  border-radius: 6px;
  margin: 0 4px;
  transition: all 0.2s ease;
}

.equipment-tree .el-tree-node__content:hover {
  background-color: #f0f2f5;
}

.detail-panel {
  height: 100%;
  flex: 1;
  overflow: hidden;
  padding: 16px;
  background-color: #fff;
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafbfc;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
}

.device-detail {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
}

.info-card,
.stats-card {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease;
}

.info-card:hover,
.stats-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.info-card:last-child,
.stats-card:last-child {
  margin-bottom: 0;
}

.info-card .el-card__header,
.stats-card .el-card__header {
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-actions .el-button {
  border-radius: 6px;
  font-size: 12px;
  padding: 6px 12px;
}

.device-info {
  padding: 20px;
}

.info-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  line-height: 1.6;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.info-label {
  font-weight: 500;
  color: #606266;
  margin-right: 16px;
  min-width: 100px;
  font-size: 14px;
}

.info-value {
  color: #303133;
  flex: 1;
  font-size: 14px;
  font-weight: 400;
}

.stats-info {
  padding: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #e9ecef;
  transition: background 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stat-item.online {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border-color: #b3d8ff;
}

.stat-item.online::before {
  background: #67c23a;
}

.stat-item.offline {
  background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
  border-color: #fbc4c4;
}

.stat-item.offline::before {
  background: #f56c6c;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 8px;
  line-height: 1;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.stat-item.online .stat-number {
  color: #67c23a;
}

.stat-item.offline .stat-number {
  color: #f56c6c;
}

.stat-label {
  font-size: 13px;
  color: #606266;
  line-height: 1.2;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .equipment-container {
    flex-direction: column;
    height: auto;
  }

  .tree-panel,
  .detail-panel {
    width: 100% !important;
    flex: none;
    padding: 0;
  }

  .tree-panel {
    margin-bottom: 16px;
  }

  .tree-card {
    height: 400px;
  }

  .resize-handle {
    display: none;
  }
}

@media (max-width: 768px) {
  .equipment-center {
    padding: 8px;
  }

  .equipment-container {
    margin: 0;
    border-radius: 6px;
    height: calc(100vh - 80px);
  }

  .detail-panel {
    padding: 12px;
  }

  .header-actions {
    flex-direction: column;
    gap: 6px;
  }

  .header-actions .el-button {
    width: 100%;
    font-size: 13px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 6px 0;
  }

  .info-label {
    margin-bottom: 4px;
    min-width: auto;
    font-size: 13px;
  }

  .info-value {
    font-size: 13px;
  }

  .stat-item {
    padding: 16px 12px;
  }

  .stat-number {
    font-size: 24px;
  }

  .stat-label {
    font-size: 12px;
  }

  .device-info {
    padding: 16px;
  }

  .stats-info {
    padding: 16px;
  }
}
</style>