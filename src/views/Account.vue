<template>
  <div>
    <el-form
      :model="form"
      ref="form"
      label-width="80px"
      label-position="left"
      class="demo-ruleForm"
    >
      <el-row :gutter="20">
        <el-col :span="4">
          <el-form-item :label="$t('account.form.name')" prop="name">
            <el-input
              v-model="form.name"
              :placeholder="$t('account.form.name')"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item :label="$t('account.form.createTime')" prop="date">
            <el-date-picker
              v-model="form.date"
              value-format="timestamp"
              type="daterange"
              :range-separator="$t('public.to')"
              :start-placeholder="$t('public.startDate')"
              :end-placeholder="$t('public.endDate')"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="space-between">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="add()"
          size="mini"
          >{{ $t("account.button.addAccount") }}</el-button
        >
        <el-col :span="4">
          <el-button @click="resetForm('form', 'getList')" size="mini">{{
            $t("public.reset")
          }}</el-button>
          <el-button type="primary" @click="searchForm()" size="mini">{{
            $t("public.search")
          }}</el-button>
        </el-col>
      </el-row>
    </el-form>

    <div style="height: 60vh; background-color: #ccc; margin: 10px 0">
      <el-table
        v-loading="isLoading"
        :data="dataList"
        style="width: 100%"
        border
        height="100%"
      >
        <el-table-column
          prop="num"
          :label="$t('account.table.num')"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          :label="$t('account.table.name')"
          width="180"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="account"
          :label="$t('account.table.account')"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="created_at"
          :label="$t('account.table.created_at')"
          align="center"
        >
          <template slot-scope="scope">
            {{ $formatTimeStamp(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('public.operation')"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="edit(scope.row)">{{
              $t("public.edit")
            }}</el-button>
            <el-popconfirm
              :title="$t('account.table.confirmDelete')"
              style="margin-left: 10px"
              @confirm="del(scope.row.id)"
            >
              <el-button
                type="text"
                style="color: #ff0000"
                size="small"
                slot="reference"
                >{{ $t("public.delete") }}</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-row :gutter="20" type="flex" justify="end">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </el-row>

    <el-dialog
      :title="
        isEdit
          ? $t('account.dialog.title.edit')
          : $t('account.dialog.title.add')
      "
      :visible.sync="isShow"
      width="30%"
      @close="close"
    >
      <el-form
        :model="addForm"
        :rules="rules"
        ref="addForm"
        label-width="80px"
        label-position="left"
        class="demo-ruleForm"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item :label="$t('account.form.name')" prop="name" required>
              <el-input
                v-model="addForm.name"
                :placeholder="$t('account.form.namePlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              :label="$t('account.form.account')"
              prop="account"
              required
            >
              <el-input
                v-model="addForm.account"
                :placeholder="$t('account.form.accountPlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('account.form.password')" prop="password">
              <el-input
                v-model="addForm.password"
                :placeholder="$t('account.form.passwordPlaceholder')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="isShow = false">{{ $t("public.cancel") }}</el-button>
        <el-button type="primary" @click="save()">{{
          $t("public.confirm")
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getList, add, edit, del } from "@/api/account.js";
export default {
  data() {
    return {
      dataList: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      form: {
        name: "",
        date: [],
      },
      addForm: {
        name: "",
        password: "",
        account: "",
      },
      id: "",
      rules: {
        name: [
          {
            required: true,
            message: this.$i18n.t("account.form.namePlaceholder"),
            trigger: "blur",
          },
        ],
        account: [
          {
            required: true,
            message: this.$i18n.t("account.form.accountPlaceholder"),
            trigger: "blur",
          },
        ],
      },
      isShow: false,
      isEdit: false,
      isLoading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.isLoading = true;
      getList({
        page: this.pageNum,
        pageSize: this.pageSize,
        name: this.form.name,
        created_at_start:
          this.form.date.length > 0 ? this.form.date[0] / 1000 : "",
        created_at_end:
          this.form.date.length > 0 ? this.form.date[1] / 1000 : "",
      }).then((res) => {
        if (res.code == 0) {
          this.dataList = res.data.data;
          this.total = res.data.total;
          this.isLoading = false;
        }
      });
    },
    //新建模板
    add() {
      this.isShow = true;
    },
    edit(row) {
      this.id = row.id;
      this.addForm.name = row.name;
      this.addForm.account = row.account;
      this.addForm.password = row.password;
      this.isEdit = true;
      this.isShow = true;
    },
    save() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            edit(
              {
                account: this.addForm.account,
                password: this.addForm.password,
                name: this.addForm.name,
              },
              this.id
            ).then((res) => {
              this.$message({
                type: "success",
                message: this.$i18n.t("public.editSuccess"),
              });
              this.$refs.addForm.resetFields();

              this.getList();
            });
          } else {
            add({
              account: this.addForm.account,
              password: this.addForm.password,
              name: this.addForm.name,
            }).then((res) => {
              this.$message({
                type: "success",
                message: this.$i18n.t("public.addSuccess"),
              });
                this.getList();
            });
          }
          this.isShow = false;
          this.getList();
        }
      });
    },
    del(id) {
      del(id).then((res) => {
        this.$message({
          type: "success",
          message: this.$i18n.t("public.deleteSuccess"),
        });
        this.getList();
      });
    },
    close() {},
    //搜索
    searchForm() {
      this.getList();
    },
    //重置
    resetForm() {
      this.pageNum = 1;
      this.$refs.form.resetFields();
      this.getList();
    },
    handleSizeChange(val) {
      this.pageNum = 1;
      this.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getList();
    },
  },
};
</script>

<style scoped>
.flex {
  display: flex;
}

.img {
  width: 40px;
  height: 40px;
}
</style>
